<script lang="ts" setup>
import ModelRenderer from '/@/components/ModelRenderer/ModelRenderer.vue';
import { message } from '@geega-ui-plus/ant-design-vue';
import { shallowReactive } from 'vue';
import { chooseFiles } from '/@/components/ImportFile/file';
import { GeegaBasicTitle } from '/@/components/GeegaBasicTitle';
import { buildUUID } from '/@/utils/uuid';
import VModelFile from '/@/components/ModelRenderer/VModelFile.vue';
import { VModelFileFeature } from '/@/components/ModelRenderer/enum';

const state = shallowReactive({
  url: '',
  fileCache: {} as Record<string, File>,
});

async function uploadModel() {
  const files = await chooseFiles({
    accept: '.glb',
    multiple: false,
  });

  const file = files.at(0);
  if (!file?.name.endsWith('.glb')) {
    message.warn('请选择 .glb 类型的模型！');
    return;
  }

  state.url = buildUUID() + 'test.glb';

  state.fileCache = {
    [state.url]: file,
  };
}
</script>

<template>
  <div class="relative flex h-full flex-col p-2 gap-2">
    <div class="h-full flex flex-col">
      <div class="flex mb-2">
        <div class="flex-1">
          <GeegaBasicTitle class="py-0!"> 模型展示 </GeegaBasicTitle>
        </div>

        <div class="flex gap-2">
          <a-button type="primary" @click="uploadModel">上传模型</a-button>
        </div>
      </div>

      <div class="flex-1 model-render-wrapper">
        <div class="h-full dark-ignore-style" :style="{ background: '#ccc' }">
          <ModelRenderer :inspector="false">
            <VModelFile
              debug
              keep-material
              :features="VModelFileFeature.FocusOnSelected"
              :url="state.url"
              :model-cache="state.fileCache"
            />
          </ModelRenderer>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.model-render-wrapper {
  border: 1px solid gray;
}

.model-wrapper {
  display: flex;
  flex-direction: column;
}

.problems-wrapper {
  overflow: auto hidden;

  &::-webkit-scrollbar {
    height: 4px;
  }
}

.problems {
  overflow: auto;
  width: fit-content;
  gap: 8px;
  display: flex;
}

.problem-box {
  width: 240px;
  cursor: pointer;

  &.is-active {
    border: 1px solid @primary-color;
  }
}

.add-box {
  width: 240px;
  min-height: 100px;

  border: 1px solid #e8e8e8;
  border-radius: 4px;
  transition: all 0.3s;

  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.desc {
  display: flex;
  gap: 4px;

  .label {
    width: 4em;
    text-align: right;
  }

  .value {
    color: #666;
  }
}
</style>
