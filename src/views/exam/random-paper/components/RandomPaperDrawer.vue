<template>
  <BasicDrawer
    @register="registerDrawer"
    title="编辑随机试卷"
    :width="800"
    showFooter
    @ok="handleSave"
    @close="handleClose"
  >
    <BasicForm @register="registerForm">
      <template #params-filter-slot="{ model }">
        <ParamsConfig
          v-model:value="model.questionPropertyList"
          ref="paramsConfigRef"
          :bankTypeId="model.bankTypeId"
          :control-column-key-once="false"
          @valueSplice="formActions.validateFields(['questionPropertyList'])"
        />
      </template>
      <template #question-type="{ model }">
        <QuestionTypes
          v-model:value="model.questionTypeList"
          @update:value="onQuestionTypesChanged"
        />
      </template>
      <template #all-score-slot="{ model }">
        <a-input :value="`${calcAllScore(model.questionTypeList)}`" disabled suffix="分"></a-input>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { ref, nextTick } from 'vue';
import { message } from '@geega-ui-plus/ant-design-vue';
import { BasicDrawer, BasicForm, useDrawer, useForm } from '@geega-ui-plus/geega-ui';
import { examFormSchemas, calcAllScore } from '../../home/<USER>/exam-form';
import ParamsConfig from '../../home/<USER>/ParamsConfig.vue';
import QuestionTypes from '../../home/<USER>/QuestionTypes.vue';
import { V1ManageExamPaperPut, V1ManageExamPaperPromptUpdatePost } from '/@/api/cddc.req';
import type { ResultQuestionTypeList } from '/@/api/cddc.model';
import { clamp } from 'lodash-es';
import { useLoadingFn } from '/@/composables';

interface Emits {
  (e: 'success'): void;
}

const emit = defineEmits<Emits>();

const paramsConfigRef = ref<InstanceType<typeof ParamsConfig>>();
const isEditMode = ref(false);
const currentId = ref<string>('');

function createInitModel() {
  return {
    questionTypeList: [],
    questionPropertyList: [{}],
  };
}

// 使用 BasicDrawer
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer();

// 使用 BasicForm - 参考 ExamCreate.vue 的配置
const [registerForm, formActions] = useForm({
  model: createInitModel(),
  schemas: examFormSchemas({
    hideType:true,
    onChange: () => {
      paramsConfigRef.value?.clearConfig?.();
    },
  }),
  showActionButtonGroup: false,
});

// 题型变化处理 - 参考 ExamCreate.vue
async function onQuestionTypesChanged(types: ResultQuestionTypeList[]) {
  const maxValue = calcAllScore(types);

  await formActions.updateSchema({
    field: 'standardScore',
    componentProps: {
      min: 0,
      max: maxValue,
      style: {
        width: '100%',
      },
    },
  });

  const oldValue = formActions.getFieldsValue().standardScore;
  const newValue = clamp(oldValue, 0, maxValue);

  if (newValue !== oldValue) {
    await formActions.setFieldsValue({
      standardScore: newValue,
    });
  }
}

// 打开抽屉并填充数据
function openDrawerWithData(record?: any) {
  openDrawer(true);

  if (record) {
    isEditMode.value = true;
    currentId.value = record.id;

    nextTick(() => {
      fillFormData(record);
    });
  } else {
    isEditMode.value = false;
    currentId.value = '';

    nextTick(() => {
      formActions.resetFields();
      formActions.setFieldsValue(createInitModel());
      formActions.clearValidate();
    });
  }
}

// 填充表单数据 - 参考试卷创建的字段结构
function fillFormData(rec: any) {
  if (!rec) return;

  // 设置基础表单字段 - 使用与 ExamCreate 相同的字段名
  formActions.setFieldsValue({
    title: rec.name ?? rec.title ?? '', // 试卷标题
    bankTypeId: rec.bankTypeId ?? rec.questionBankId, // 题库ID
    type: rec.type ?? 'SKILL_INSPECTION', // 试卷类型
    standardScore: rec.standardScore ?? rec.passScore ?? 60, // 合格分数
    questionPoint: rec.questionPoint ?? rec.description ?? '', // 考题要点
    questionTypeList: rec.questionTypeList ?? [], // 题型列表
    questionPropertyList: rec.questionPropertyList ?? [{}], // 参数筛选
  });
}

// 关闭抽屉
function handleClose() {
  openDrawer(false);
}

// 保存 - 使用 useLoadingFn 包装
const handleSave = useLoadingFn(async () => {
  try {
    const values = await formActions.validate();

    const clonedValues = {
      ...values,
      standardScore: values.standardScore ?? 0,
      questionPropertyList: values.questionPropertyList?.filter(
        (item) => item.columnKey && item.columnValue
      ),
    };
    if (isEditMode.value && currentId.value) {
      // 编辑模式 - 更新试卷
      await V1ManageExamPaperPromptUpdatePost({
        id: currentId.value,
        ...clonedValues,
      });
      message.success('更新成功');
    } else {
      // 创建模式 - 创建新试卷
      await V1ManageExamPaperPut(clonedValues);
      message.success('创建成功');
    }

    emit('success');
    openDrawer(false);
  } catch (error) {
    console.error('保存失败：', error);
    message.error('保存失败，请重试');
  }
});

// 暴露方法给父组件调用
defineExpose({
  openDrawerWithData,
});
</script>

<style lang="less" scoped>
:deep(.@{ant-prefix}-form-item-control-input-content) > div > div {
  width: 100%;
}
</style>
