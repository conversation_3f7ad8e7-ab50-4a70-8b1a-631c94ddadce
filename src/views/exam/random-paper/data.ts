import type { EnhancedColumn } from '/@/components/BasicTablePlus/types';
import type { FormSchema } from '@geega-ui-plus/geega-ui';
import {
  V1ManageCommonUserListPost,
  V1ManageQuestionBankTypeGetQuestionBankTypeListPost,
} from '/@/api/cddc.req';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';

// 随机试卷编辑表单配置
export const getRandomPaperFormSchemas = (): FormSchema[] => [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'name',
    label: '试卷名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入试卷名称',
      maxlength: 50,
    },
    rules: [
      { required: true, message: '请输入试卷名称', trigger: 'blur' },
      { min: 2, max: 50, message: '试卷名称长度在2-50个字符', trigger: 'blur' },
    ],
  },
  {
    field: 'questionBankId',
    label: '所属题库',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      api: V1ManageQuestionBankTypeGetQuestionBankTypeListPost,
      placeholder: '请选择题库',
      showSearch: true,
      fieldNames: {
        label: 'name',
        value: 'id',
        key: 'id',
      },
      optionFilterProp: 'name',
    },
    rules: [{ required: true, message: '请选择题库', trigger: 'change' }],
  },
  {
    field: 'totalScore',
    label: '满分分数',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 1,
      max: 1000,
      placeholder: '请输入满分分数',
      style: { width: '100%' },
    },
    rules: [{ required: true, message: '请输入满分分数', trigger: 'blur' }],
  },
  {
    field: 'passScore',
    label: '合格分数',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 1,
      max: 1000,
      placeholder: '请输入合格分数',
      style: { width: '100%' },
    },
    rules: [{ required: true, message: '请输入合格分数', trigger: 'blur' }],
  },
  {
    field: 'duration',
    label: '考试时长(分钟)',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 1,
      max: 300,
      placeholder: '请输入考试时长',
      style: { width: '100%' },
    },
    rules: [{ required: true, message: '请输入考试时长', trigger: 'blur' }],
  },
  {
    field: 'questionCount',
    label: '题目数量',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 1,
      max: 200,
      placeholder: '请输入题目数量',
      style: { width: '100%' },
    },
    rules: [{ required: true, message: '请输入题目数量', trigger: 'blur' }],
  },
  {
    field: 'description',
    label: '试卷描述',
    component: 'InputTextArea',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入试卷描述',
      rows: 3,
      maxlength: 500,
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Input',
    show: false,
  },
];

// 下发表单配置
export const distributeFormSchemas: FormSchema[] = [
  {
    field: 'userIds',
    label: '选择人员',
    required: true,
    component: 'TreeSelect',
    slot: 'userSelect',
  },
  {
    field: 'retryTimes',
    label: '补考次数',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 0,
      max: 10,
      precision: 0,
      placeholder: '请输入补考次数',
      style: { width: '100%' },
    },
  },
  {
    field: 'date',
    label: '考试有效期',
    component: 'RangePicker',
    componentProps: {
      getPopupContainer: () => document.body,
      popupStyle: {
        width: '570px',
        zIndex: 9999,
      },
      disabledDate: (current: Dayjs) => current.isBefore(dayjs().startOf('d')),
    },
  },
];

// 表格列配置
export function getRandomPaperColumns(): EnhancedColumn[] {
  return [
    {
      title: '试卷名称',
      dataIndex: 'name',
      width: 200,
      ellipsis: true,
      search: {
        component: 'Input',
      },
    },
    {
      title: '所属题库',
      dataIndex: 'bankTypeId',
      width: 160,
      search: {
        field: 'bankTypeId',
        component: 'ApiSelect',
        componentProps: {
          api: V1ManageQuestionBankTypeGetQuestionBankTypeListPost,
          placeholder: '请选择题库',
          showSearch: true,
          fieldNames: {
            label: 'name',
            value: 'id',
            key: 'id',
          },
          optionFilterProp: 'name',
          showArrow: true,
          maxTagCount: 'responsive',
        },
      },
    },
    {
      title: '满分分数',
      dataIndex: 'totalScore',
      width: 120,
      align: 'center',
    },
    {
      title: '合格分数',
      dataIndex: 'standardScore',
      width: 120,
      align: 'center',
    },
    {
      title: '下发人数',
      dataIndex: 'releaseUserNum',
      width: 120,
      align: 'center',
    },
  ];
}
