<script lang="ts" setup>
import { reactive } from 'vue';
import { BasicDrawer, BasicForm, useForm, useDrawerInner } from '@geega-ui-plus/geega-ui';
import { message } from '@geega-ui-plus/ant-design-vue';
import DynamicItemList from '/@/components/DynamicItemList.vue';
import {
  V1EvaluateConfigId,
  V1EvaluateConfigSelect,
  V1EvaluateTaskSavePost,
} from '/@/api/cddc.req';
import UserTreeSelect from '/@/components/UserTreeSelect.vue';
import { uniq } from 'lodash-es';
import type { TaskGroupListElement } from '/@/api/cddc.model';
import { useAsyncData } from '/@/composables';

const emit = defineEmits(['success']);

const state = reactive({
  drawerTitle: '新增评审任务',
});

const currentSelectedItem = useAsyncData(V1EvaluateConfigId, {});

const [registerDrawer, drawerActions] = useDrawerInner(async (props: any) => {
  const data = { ...props };

  if (!data.taskGroupList?.length) {
    data.taskGroupList = [{}];
  }

  await fromActions.resetFields();
  await fromActions.setFieldsValue(data);
  await fromActions.clearValidate();
});

const [registerForm, fromActions] = useForm({
  model: {},
  rowProps: {
    gutter: [24, 12],
  },
  baseColProps: { span: 24 },
  schemas: [
    {
      field: 'evaluateConfigId',
      label: '评定项目名称',
      component: 'ApiSelect',
      required: true,
      componentProps: {
        maxLength: 32,
        placeholder: '请选择',
        api: V1EvaluateConfigSelect,
        optionFilterProp: 'label',
        fieldNames: {
          label: 'evaluateName',
          value: 'id',
          key: 'id',
        },
        async onChange(value) {
          await currentSelectedItem.load({ id: value });

          const resp = currentSelectedItem.data.value;

          const reviewerList = resp.reviewerList?.map((n) => n.userId!);

          if (!reviewerList?.length) {
            return;
          }

          const taskGroupList: TaskGroupListElement[] = fromActions.getFieldsValue()?.taskGroupList;

          if (!taskGroupList?.length) {
            return;
          }

          taskGroupList.forEach((item) => {
            const ids = [...reviewerList, ...(item.reviewerGroupList || [])];

            item.reviewerGroupList = uniq(ids);
          });

          await fromActions.setFieldsValue({
            taskGroupList,
          });
        },
      },
    },
    {
      field: 'taskGroupList',
      label: '评定人员设置',
      component: 'Input',
      required: true,
      slot: 'groups',
      rules: [
        {
          async validator(_rule, value) {
            const valid = (value || []).every(
              (item) => item.assessorGroupList?.length && item.reviewerGroupList?.length
            );

            if (!valid) {
              throw new Error('请选择待评定人员和评审人员');
            }
          },
        },
      ],
    },
  ],
  showActionButtonGroup: false,
});

async function handleSubmit() {
  if (!drawerActions.getVisible?.value) {
    return;
  }

  drawerActions.setDrawerProps({
    visible: true,
    confirmLoading: true,
  });

  try {
    const data = await fromActions.validate();

    data.taskGroupList = (data.taskGroupList || []).map((item, index) => ({
      ...item,
      sort: index,
    }));

    await V1EvaluateTaskSavePost(data);
    message.success('创建成功');

    emit('success');
    drawerActions.setDrawerProps({
      visible: false,
      confirmLoading: false,
    });
  } catch (error) {
    drawerActions.setDrawerProps({
      visible: true,
      confirmLoading: false,
    });

    console.error(error);
  }
}

function createNewItem() {
  const resp = currentSelectedItem.data.value;

  const ids = resp.reviewerList?.map((n) => n.userId!);

  return {
    reviewerGroupList: uniq(ids),
  };
}
</script>

<template>
  <BasicDrawer
    width="600"
    :title="state.drawerTitle"
    @register="registerDrawer"
    showFooter
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #groups="{ model, field }">
        <DynamicItemList v-model:value="model[field]" :maxCount="10" :createNewItem="createNewItem">
          <template #default="{ item, removeItem, index }">
            <div class="box mb-2">
              <div class="box-title flex">
                <div class="flex-1 font-bold"> 待评定人员组 {{ index + 1 }} </div>
                <a-button
                  type="link"
                  :disabled="model[field]?.length == 1"
                  danger
                  @click="removeItem()"
                  >删除</a-button
                >
              </div>
              <div class="flex flex-col gap-1 px-2">
                <div class="flex items-center gap-1">
                  <label class="w-5em"> 待评定人员</label>
                  <UserTreeSelect
                    class="w-0 flex-1"
                    v-model:value="item.assessorGroupList"
                    placeholder="请选择"
                  />
                </div>
                <div class="flex items-center gap-1">
                  <label class="w-5em text-right"> 评审人员</label>
                  <UserTreeSelect
                    class="w-0 flex-1"
                    v-model:value="item.reviewerGroupList"
                    placeholder="请选择"
                  />
                </div>
              </div>
            </div>
          </template>
        </DynamicItemList>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<style lang="less" scoped>
.box {
  border: 1px dashed gray;
  border-radius: 4px;
  padding: 8px;

  &-title {
    color: @primary-color;
  }
}
</style>
