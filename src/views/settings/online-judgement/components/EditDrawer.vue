<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { BasicDrawer, BasicForm, useForm, useDrawerInner } from '@geega-ui-plus/geega-ui';
import { message } from '@geega-ui-plus/ant-design-vue';
import DynamicItemList from '/@/components/DynamicItemList.vue';
import { V1EvaluateConfigSavePost } from '/@/api/cddc.req';
import type { V1EvaluateConfigIDGetResponseResult } from '/@/api/cddc.model';
import UserTreeSelect from '/@/components/UserTreeSelect.vue';

const emit = defineEmits(['success']);

const state = reactive({
  drawerTitle: '编辑评定项目',
});

const currentData = ref<V1EvaluateConfigIDGetResponseResult>({});

const [registerDrawer, drawerActions] = useDrawerInner(
  async (props: V1EvaluateConfigIDGetResponseResult) => {
    props = props || {};

    state.drawerTitle = props?.id ? '编辑评定项目' : '新增评定项目';
    currentData.value = props;

    await formActions.resetFields();
    await formActions.setFieldsValue({
      ...props,
      evaluateProjectList: (props.evaluateProjectList || [undefined]).map((item) => ({
        name: item,
      })),
      reviewerList: (props.reviewerList || []).map((n) => n.userId),

      // 展示用
      _result: 'pass',
    });
    await formActions.clearValidate();
  }
);

const [registerForm, formActions] = useForm({
  model: {},
  rowProps: {
    gutter: [24, 12],
  },
  baseColProps: { span: 24 },
  schemas: [
    {
      field: 'evaluateName',
      label: '项目名称',
      component: 'Input',
      required: true,
      componentProps: {
        maxLength: 20,
        placeholder: '请输入项目名称',
      },
    },
    {
      field: 'reviewerList',
      label: '评定人',
      component: 'Input',
      slot: 'userSelect',
      rules: [
        {
          required: true,
          async validator(_rule, value) {
            if ((value?.length || 0) <= 0) {
              throw new Error(`请选择评定人`);
            }
          },
        },
      ],
    },
    {
      field: 'remark',
      label: '项目说明',
      component: 'InputTextArea',
      componentProps: {
        maxLength: 200,
        placeholder: '请输入项目说明',
        rows: 4,
      },
    },
    {
      field: 'evaluateProjectList',
      label: '评审项目',
      component: 'Input',
      slot: 'projects',
    },
    {
      field: '_result',
      label: '评定结果',
      component: 'RadioGroup',
      componentProps: {
        options: [
          { label: '通过', value: 'pass' },
          { label: '不通过', value: 'reject' },
        ],
        disabled: true,
      },
    },
  ],
  showActionButtonGroup: false,
});

async function handleSubmit() {
  if (!drawerActions.getVisible?.value) {
    return;
  }

  drawerActions.setDrawerProps({
    visible: true,
    confirmLoading: true,
  });

  try {
    const data = await formActions.validate();
    const id = currentData.value.id;

    const evaluateProjectList = (data.evaluateProjectList || [])
      .map((item) => item.name)
      .filter((item) => item);

    await V1EvaluateConfigSavePost({
      ...data,
      id,
      evaluateProjectList,
    });

    message.success(id ? '修改成功' : '创建成功');

    emit('success');
    drawerActions.setDrawerProps({
      visible: false,
      confirmLoading: false,
    });
  } catch (error) {
    drawerActions.setDrawerProps({
      visible: true,
      confirmLoading: false,
    });

    console.error(error);
  }
}
</script>

<template>
  <BasicDrawer
    width="600"
    :title="state.drawerTitle"
    @register="registerDrawer"
    showFooter
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #projects="{ model, field }">
        <DynamicItemList v-model:value="model[field]" :maxCount="10">
          <template #default="{ item, removeItem }">
            <div class="flex gap-1">
              <a-input v-model:value="item.name" :maxlength="20" placeholder="请输入" />
              <a-button type="link" danger @click="removeItem()">删除</a-button>
            </div>
          </template>
        </DynamicItemList>
      </template>

      <template #userSelect="{ model, field }">
        <UserTreeSelect v-model:value="model[field]" />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<style lang="less" scoped></style>
